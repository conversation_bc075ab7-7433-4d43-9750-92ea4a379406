import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:smart_kirana/models/order_model.dart';
import 'package:smart_kirana/services/maps_service.dart';
import 'package:smart_kirana/utils/constants.dart';

class OrderTrackingMap extends StatefulWidget {
  final OrderModel order;
  final double height;

  const OrderTrackingMap({super.key, required this.order, this.height = 250});

  @override
  State<OrderTrackingMap> createState() => _OrderTrackingMapState();
}

class _OrderTrackingMapState extends State<OrderTrackingMap> {
  GoogleMapController? _mapController;
  final MapsService _mapsService = MapsService();

  Set<Marker> _markers = {};
  Set<Polyline> _polylines = {};
  bool _isLoading = true;
  String? _error;
  StreamSubscription<LatLng>? _locationSubscription;

  @override
  void initState() {
    super.initState();
    _initializeMap();
  }

  @override
  void dispose() {
    _locationSubscription?.cancel();
    _mapController?.dispose();
    super.dispose();
  }

  Future<void> _initializeMap() async {
    try {
      // Skip complex initialization for now - just setup basic markers
      // Check if order has delivery coordinates
      if (widget.order.deliveryLatitude == null ||
          widget.order.deliveryLongitude == null) {
        setState(() {
          _error = 'Delivery location not available';
          _isLoading = false;
        });
        return;
      }

      await _setupBasicMapData();

      // Skip simulation for now to avoid API calls
      // if (widget.order.status == OrderStatus.shipped) {
      //   _startLocationSimulation();
      // }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Error setting up map: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _setupBasicMapData() async {
    final deliveryLocation = LatLng(
      widget.order.deliveryLatitude!,
      widget.order.deliveryLongitude!,
    );

    LatLng? currentLocation;
    if (widget.order.currentLatitude != null &&
        widget.order.currentLongitude != null) {
      currentLocation = LatLng(
        widget.order.currentLatitude!,
        widget.order.currentLongitude!,
      );
    }

    // Create basic markers without using maps service
    _markers.clear();

    // Delivery location marker
    _markers.add(
      Marker(
        markerId: const MarkerId('delivery_location'),
        position: deliveryLocation,
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
        infoWindow: const InfoWindow(
          title: 'Delivery Location',
          snippet: 'Your order will be delivered here',
        ),
      ),
    );

    // Current location marker (delivery agent)
    if (currentLocation != null) {
      _markers.add(
        Marker(
          markerId: const MarkerId('current_location'),
          position: currentLocation,
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
          infoWindow: InfoWindow(
            title: widget.order.deliveryAgentName ?? 'Delivery Agent',
            snippet: 'Current location',
          ),
        ),
      );
    }

    // Skip route calculation for now to avoid API calls
    _polylines.clear();
  }

  void _startLocationSimulation() {
    if (widget.order.deliveryLatitude == null ||
        widget.order.deliveryLongitude == null) {
      return;
    }

    final deliveryLocation = LatLng(
      widget.order.deliveryLatitude!,
      widget.order.deliveryLongitude!,
    );

    // Start from a location 5km away (simulated)
    final startLocation = LatLng(
      deliveryLocation.latitude + 0.045, // ~5km north
      deliveryLocation.longitude + 0.045, // ~5km east
    );

    _locationSubscription = _mapsService
        .simulateDeliveryAgentMovement(
          startLocation: startLocation,
          endLocation: deliveryLocation,
          interval: const Duration(seconds: 5),
        )
        .listen((newLocation) {
          _updateDeliveryAgentLocation(newLocation);
        });
  }

  void _updateDeliveryAgentLocation(LatLng newLocation) {
    setState(() {
      // Update current location marker
      _markers.removeWhere(
        (marker) => marker.markerId.value == 'current_location',
      );
      _markers.add(
        Marker(
          markerId: const MarkerId('current_location'),
          position: newLocation,
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
          infoWindow: InfoWindow(
            title: widget.order.deliveryAgentName ?? 'Delivery Agent',
            snippet: 'Current location',
          ),
        ),
      );
    });

    // Update camera to show both locations
    if (_mapController != null) {
      final deliveryLocation = LatLng(
        widget.order.deliveryLatitude!,
        widget.order.deliveryLongitude!,
      );

      final cameraPosition = _mapsService.getCameraPositionForBounds(
        deliveryLocation: deliveryLocation,
        currentLocation: newLocation,
      );

      _mapController!.animateCamera(
        CameraUpdate.newCameraPosition(cameraPosition),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.medium),
      ),
      child: Container(
        height: widget.height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppBorderRadius.medium),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(AppBorderRadius.medium),
          child: _buildMapContent(),
        ),
      ),
    );
  }

  Widget _buildMapContent() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: AppPadding.small),
            Text('Loading map...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 48, color: AppColors.error),
            const SizedBox(height: AppPadding.small),
            Text(
              'Map Error',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppPadding.small / 2),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppPadding.medium,
              ),
              child: Text(
                _error!,
                style: AppTextStyles.bodySmall,
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: AppPadding.medium),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                  _error = null;
                });
                _initializeMap();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (widget.order.deliveryLatitude == null ||
        widget.order.deliveryLongitude == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.location_off,
              size: 48,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: AppPadding.small),
            Text(
              'Location Not Available',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppPadding.small / 2),
            Text(
              'Delivery location will be updated soon',
              style: AppTextStyles.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    final deliveryLocation = LatLng(
      widget.order.deliveryLatitude!,
      widget.order.deliveryLongitude!,
    );

    LatLng? currentLocation;
    if (widget.order.currentLatitude != null &&
        widget.order.currentLongitude != null) {
      currentLocation = LatLng(
        widget.order.currentLatitude!,
        widget.order.currentLongitude!,
      );
    }

    // Simple camera position calculation without using maps service
    CameraPosition initialCameraPosition;
    if (currentLocation == null) {
      initialCameraPosition = CameraPosition(
        target: deliveryLocation,
        zoom: 15.0,
      );
    } else {
      // Calculate center point between delivery and current location
      final centerLat =
          (deliveryLocation.latitude + currentLocation.latitude) / 2;
      final centerLng =
          (deliveryLocation.longitude + currentLocation.longitude) / 2;

      initialCameraPosition = CameraPosition(
        target: LatLng(centerLat, centerLng),
        zoom: 14.0,
      );
    }

    return GoogleMap(
      initialCameraPosition: initialCameraPosition,
      markers: _markers,
      polylines: _polylines,
      onMapCreated: (GoogleMapController controller) {
        _mapController = controller;
      },
      myLocationEnabled: false,
      myLocationButtonEnabled: false,
      zoomControlsEnabled: true,
      mapToolbarEnabled: false,
      compassEnabled: true,
      trafficEnabled: false,
      buildingsEnabled: true,
      indoorViewEnabled: false,
      mapType: MapType.normal,
      // Add error handling for map creation
      onCameraMove: (CameraPosition position) {
        // Handle camera movement if needed
      },
    );
  }
}
