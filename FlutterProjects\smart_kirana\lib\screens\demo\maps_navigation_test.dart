import 'package:flutter/material.dart';
import 'package:smart_kirana/screens/demo/minimal_map_test.dart';
import 'package:smart_kirana/screens/demo/maps_demo_screen.dart';
import 'package:smart_kirana/screens/demo/maps_test_screen.dart';
import 'package:smart_kirana/utils/constants.dart';

class MapsNavigationTest extends StatelessWidget {
  const MapsNavigationTest({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Maps Testing'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppPadding.medium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppPadding.medium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Maps Testing Options', style: AppTextStyles.heading2),
                    const SizedBox(height: AppPadding.small),
                    Text(
                      'Use these screens to test different aspects of the Google Maps integration:',
                      style: AppTextStyles.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: AppPadding.medium),

            // Minimal Map Test
            Card(
              child: ListTile(
                leading: const Icon(Icons.map, color: AppColors.primary),
                title: const Text('Minimal Map Test'),
                subtitle: const Text(
                  'Basic Google Maps widget with simple marker',
                ),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const MinimalMapTest(),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: AppPadding.small),

            // Maps Service Test
            Card(
              child: ListTile(
                leading: const Icon(Icons.settings, color: AppColors.secondary),
                title: const Text('Maps Service Test'),
                subtitle: const Text('Test maps service initialization'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const MapsTestScreen(),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: AppPadding.small),

            // Full Demo
            Card(
              child: ListTile(
                leading: const Icon(
                  Icons.delivery_dining,
                  color: AppColors.accent,
                ),
                title: const Text('Order Tracking Demo'),
                subtitle: const Text('Full order tracking with maps'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const MapsDemoScreen(),
                    ),
                  );
                },
              ),
            ),

            const Spacer(),

            Container(
              padding: const EdgeInsets.all(AppPadding.medium),
              decoration: BoxDecoration(
                color: AppColors.background,
                borderRadius: BorderRadius.circular(AppBorderRadius.medium),
                border: Border.all(
                  color: AppColors.primary.withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Troubleshooting:',
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppPadding.small),
                  Text(
                    '1. Start with Minimal Map Test',
                    style: AppTextStyles.bodySmall,
                  ),
                  Text(
                    '2. If that works, try Maps Service Test',
                    style: AppTextStyles.bodySmall,
                  ),
                  Text(
                    '3. Finally test the full Order Tracking Demo',
                    style: AppTextStyles.bodySmall,
                  ),
                  const SizedBox(height: AppPadding.small),
                  Text(
                    'If Minimal Map Test fails, the issue is with basic Google Maps setup.',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.error,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
