import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;

class MapsService {
  // Singleton pattern
  static final MapsService _instance = MapsService._internal();
  factory MapsService() => _instance;
  MapsService._internal();

  // Properties
  String? _apiKey;
  bool _isInitialized = false;
  String? _error;

  // Getters
  bool get isInitialized => _isInitialized;
  String? get error => _error;

  // Initialize the maps service
  Future<bool> initialize() async {
    try {
      _apiKey = dotenv.env['GOOGLE_MAPS_API_KEY'];
      if (_apiKey == null || _apiKey!.isEmpty) {
        _error = 'Google Maps API key not found in environment variables';
        return false;
      }
      _isInitialized = true;
      _error = null;
      return true;
    } catch (e) {
      _error = 'Failed to initialize Maps Service: $e';
      return false;
    }
  }

  // Calculate distance between two points using Haversine formula
  double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // Earth's radius in kilometers

    double dLat = _degreesToRadians(lat2 - lat1);
    double dLon = _degreesToRadians(lon2 - lon1);

    double a =
        sin(dLat / 2) * sin(dLat / 2) +
        cos(_degreesToRadians(lat1)) *
            cos(_degreesToRadians(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);

    double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    return earthRadius * c;
  }

  // Convert degrees to radians
  double _degreesToRadians(double degrees) {
    return degrees * (pi / 180);
  }

  // Get route between two points using Google Directions API
  Future<Map<String, dynamic>?> getRoute({
    required LatLng origin,
    required LatLng destination,
    String travelMode = 'driving',
  }) async {
    if (!_isInitialized || _apiKey == null) {
      _error = 'Maps service not initialized';
      return null;
    }

    try {
      final String url =
          'https://maps.googleapis.com/maps/api/directions/json'
          '?origin=${origin.latitude},${origin.longitude}'
          '&destination=${destination.latitude},${destination.longitude}'
          '&mode=$travelMode'
          '&key=$_apiKey';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['status'] == 'OK' && data['routes'].isNotEmpty) {
          return data;
        } else {
          _error = 'No route found: ${data['status']}';
          return null;
        }
      } else {
        _error = 'Failed to get route: ${response.statusCode}';
        return null;
      }
    } catch (e) {
      _error = 'Error getting route: $e';
      return null;
    }
  }

  // Decode polyline points from Google Directions API response
  List<LatLng> decodePolyline(String polyline) {
    List<LatLng> points = [];
    int index = 0;
    int len = polyline.length;
    int lat = 0;
    int lng = 0;

    while (index < len) {
      int b;
      int shift = 0;
      int result = 0;
      do {
        b = polyline.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int dlat = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lat += dlat;

      shift = 0;
      result = 0;
      do {
        b = polyline.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int dlng = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lng += dlng;

      points.add(LatLng(lat / 1E5, lng / 1E5));
    }

    return points;
  }

  // Get estimated delivery time based on distance and traffic
  String getEstimatedDeliveryTime(double distanceKm) {
    // Average delivery speed considering traffic and stops
    const double averageSpeedKmh = 25.0;

    // Calculate time in hours
    double timeHours = distanceKm / averageSpeedKmh;

    // Add buffer time for preparation and delivery
    timeHours += 0.25; // 15 minutes buffer

    // Convert to minutes
    int timeMinutes = (timeHours * 60).round();

    if (timeMinutes < 60) {
      return '$timeMinutes minutes';
    } else {
      int hours = timeMinutes ~/ 60;
      int minutes = timeMinutes % 60;
      if (minutes == 0) {
        return '$hours ${hours == 1 ? 'hour' : 'hours'}';
      } else {
        return '$hours ${hours == 1 ? 'hour' : 'hours'} $minutes minutes';
      }
    }
  }

  // Create markers for order tracking
  Set<Marker> createOrderTrackingMarkers({
    required LatLng deliveryLocation,
    LatLng? currentLocation,
    String? deliveryAgentName,
  }) {
    Set<Marker> markers = {};

    // Delivery location marker
    markers.add(
      Marker(
        markerId: const MarkerId('delivery_location'),
        position: deliveryLocation,
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
        infoWindow: const InfoWindow(
          title: 'Delivery Location',
          snippet: 'Your order will be delivered here',
        ),
      ),
    );

    // Current location marker (delivery agent)
    if (currentLocation != null) {
      markers.add(
        Marker(
          markerId: const MarkerId('current_location'),
          position: currentLocation,
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
          infoWindow: InfoWindow(
            title: deliveryAgentName ?? 'Delivery Agent',
            snippet: 'Current location',
          ),
        ),
      );
    }

    return markers;
  }

  // Create polyline for route
  Set<Polyline> createRoutePolyline(List<LatLng> routePoints) {
    return {
      Polyline(
        polylineId: const PolylineId('delivery_route'),
        points: routePoints,
        color: Colors.blue,
        width: 4,
        patterns: [],
      ),
    };
  }

  // Get camera position to show both markers
  CameraPosition getCameraPositionForBounds({
    required LatLng deliveryLocation,
    LatLng? currentLocation,
  }) {
    if (currentLocation == null) {
      return CameraPosition(target: deliveryLocation, zoom: 15.0);
    }

    // Calculate bounds
    double minLat = min(deliveryLocation.latitude, currentLocation.latitude);
    double maxLat = max(deliveryLocation.latitude, currentLocation.latitude);
    double minLng = min(deliveryLocation.longitude, currentLocation.longitude);
    double maxLng = max(deliveryLocation.longitude, currentLocation.longitude);

    // Calculate center
    LatLng center = LatLng((minLat + maxLat) / 2, (minLng + maxLng) / 2);

    // Calculate zoom level based on distance
    double distance = calculateDistance(
      deliveryLocation.latitude,
      deliveryLocation.longitude,
      currentLocation.latitude,
      currentLocation.longitude,
    );

    double zoom = 15.0;
    if (distance > 10) {
      zoom = 10.0;
    } else if (distance > 5) {
      zoom = 12.0;
    } else if (distance > 1) {
      zoom = 14.0;
    }

    return CameraPosition(target: center, zoom: zoom);
  }

  // Simulate delivery agent movement (for demo purposes)
  Stream<LatLng> simulateDeliveryAgentMovement({
    required LatLng startLocation,
    required LatLng endLocation,
    Duration interval = const Duration(seconds: 10),
  }) async* {
    const int steps = 20;
    double latStep = (endLocation.latitude - startLocation.latitude) / steps;
    double lngStep = (endLocation.longitude - startLocation.longitude) / steps;

    for (int i = 0; i <= steps; i++) {
      yield LatLng(
        startLocation.latitude + (latStep * i),
        startLocation.longitude + (lngStep * i),
      );

      if (i < steps) {
        await Future.delayed(interval);
      }
    }
  }
}
