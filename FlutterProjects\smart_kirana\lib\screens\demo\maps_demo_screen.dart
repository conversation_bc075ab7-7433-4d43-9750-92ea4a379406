import 'package:flutter/material.dart';
import 'package:smart_kirana/models/order_model.dart';
import 'package:smart_kirana/utils/constants.dart';
import 'package:smart_kirana/widgets/order_tracking_map.dart';

class MapsDemoScreen extends StatefulWidget {
  const MapsDemoScreen({super.key});

  @override
  State<MapsDemoScreen> createState() => _MapsDemoScreenState();
}

class _MapsDemoScreenState extends State<MapsDemoScreen> {
  late OrderModel _demoOrder;

  @override
  void initState() {
    super.initState();
    _createDemoOrder();
  }

  void _createDemoOrder() {
    // Create a demo order with sample location data
    _demoOrder = OrderModel(
      id: 'demo_order_123',
      userId: 'demo_user',
      items: [
        OrderItem(
          productId: 'demo_product',
          productName: 'Demo Product',
          productImage: '',
          price: 100.0,
          quantity: 2,
          totalPrice: 200.0,
        ),
      ],
      subtotal: 200.0,
      deliveryFee: 50.0,
      discount: 0.0,
      totalAmount: 250.0,
      orderDate: DateTime.now().subtract(const Duration(hours: 1)),
      status: OrderStatus.shipped,
      deliveryAddress: {
        'addressLine': '123 Demo Street',
        'city': 'Demo City',
        'state': 'Demo State',
        'pincode': '123456',
      },
      paymentMethod: 'Cash on Delivery',
      userName: 'Demo User',
      estimatedDeliveryTime: DateTime.now().add(const Duration(minutes: 30)),
      // Sample coordinates for Delhi area
      deliveryLatitude: 28.6139, // New Delhi
      deliveryLongitude: 77.2090,
      // Current location (delivery agent) - slightly away from delivery location
      currentLatitude: 28.6000, // Slightly south
      currentLongitude: 77.2000, // Slightly west
      deliveryAgentName: 'Raj Kumar',
      deliveryAgentPhone: '+91 9876543210',
    );
  }

  void _updateDemoOrderLocation() {
    setState(() {
      // Simulate movement towards delivery location
      final currentLat = _demoOrder.currentLatitude ?? 28.6000;
      final currentLng = _demoOrder.currentLongitude ?? 77.2000;
      final deliveryLat = _demoOrder.deliveryLatitude ?? 28.6139;
      final deliveryLng = _demoOrder.deliveryLongitude ?? 77.2090;

      // Move 10% closer to delivery location
      final newLat = currentLat + (deliveryLat - currentLat) * 0.1;
      final newLng = currentLng + (deliveryLng - currentLng) * 0.1;

      _demoOrder = OrderModel(
        id: _demoOrder.id,
        userId: _demoOrder.userId,
        items: _demoOrder.items,
        subtotal: _demoOrder.subtotal,
        deliveryFee: _demoOrder.deliveryFee,
        discount: _demoOrder.discount,
        totalAmount: _demoOrder.totalAmount,
        orderDate: _demoOrder.orderDate,
        status: _demoOrder.status,
        deliveryAddress: _demoOrder.deliveryAddress,
        paymentMethod: _demoOrder.paymentMethod,
        userName: _demoOrder.userName,
        estimatedDeliveryTime: _demoOrder.estimatedDeliveryTime,
        deliveryLatitude: _demoOrder.deliveryLatitude,
        deliveryLongitude: _demoOrder.deliveryLongitude,
        currentLatitude: newLat,
        currentLongitude: newLng,
        deliveryAgentName: _demoOrder.deliveryAgentName,
        deliveryAgentPhone: _demoOrder.deliveryAgentPhone,
      );
    });
  }

  void _changeOrderStatus(OrderStatus newStatus) {
    setState(() {
      _demoOrder = OrderModel(
        id: _demoOrder.id,
        userId: _demoOrder.userId,
        items: _demoOrder.items,
        subtotal: _demoOrder.subtotal,
        deliveryFee: _demoOrder.deliveryFee,
        discount: _demoOrder.discount,
        totalAmount: _demoOrder.totalAmount,
        orderDate: _demoOrder.orderDate,
        status: newStatus,
        deliveryAddress: _demoOrder.deliveryAddress,
        paymentMethod: _demoOrder.paymentMethod,
        userName: _demoOrder.userName,
        estimatedDeliveryTime: _demoOrder.estimatedDeliveryTime,
        deliveryLatitude: _demoOrder.deliveryLatitude,
        deliveryLongitude: _demoOrder.deliveryLongitude,
        currentLatitude: _demoOrder.currentLatitude,
        currentLongitude: _demoOrder.currentLongitude,
        deliveryAgentName: _demoOrder.deliveryAgentName,
        deliveryAgentPhone: _demoOrder.deliveryAgentPhone,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Maps Demo'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppPadding.medium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Demo Info Card
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(AppPadding.medium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Demo Order Tracking', style: AppTextStyles.heading2),
                    const SizedBox(height: AppPadding.small),
                    Text(
                      'This is a demo showing the Google Maps integration for order tracking. '
                      'The map shows the delivery location (red marker) and current delivery agent location (blue marker).',
                      style: AppTextStyles.bodyMedium,
                    ),
                    const SizedBox(height: AppPadding.small),
                    Text('Order Status: ${_demoOrder.status.name.toUpperCase()}', 
                         style: AppTextStyles.bodyMedium.copyWith(fontWeight: FontWeight.bold)),
                    if (_demoOrder.deliveryAgentName != null)
                      Text('Agent: ${_demoOrder.deliveryAgentName}', 
                           style: AppTextStyles.bodyMedium),
                  ],
                ),
              ),
            ),
            const SizedBox(height: AppPadding.medium),

            // Map Widget
            OrderTrackingMap(order: _demoOrder, height: 300),
            const SizedBox(height: AppPadding.medium),

            // Control Buttons
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(AppPadding.medium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Demo Controls', style: AppTextStyles.heading3),
                    const SizedBox(height: AppPadding.medium),
                    
                    // Update Location Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _updateDemoOrderLocation,
                        icon: const Icon(Icons.location_on),
                        label: const Text('Simulate Agent Movement'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.secondary,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(height: AppPadding.small),

                    // Status Change Buttons
                    Text('Change Order Status:', style: AppTextStyles.bodyMedium.copyWith(fontWeight: FontWeight.bold)),
                    const SizedBox(height: AppPadding.small),
                    Wrap(
                      spacing: 8,
                      children: OrderStatus.values.map((status) {
                        final isCurrentStatus = _demoOrder.status == status;
                        return ElevatedButton(
                          onPressed: isCurrentStatus ? null : () => _changeOrderStatus(status),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: isCurrentStatus ? AppColors.primary : AppColors.background,
                            foregroundColor: isCurrentStatus ? Colors.white : AppColors.textPrimary,
                          ),
                          child: Text(status.name.toUpperCase()),
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: AppPadding.medium),

            // Location Info Card
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(AppPadding.medium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Location Information', style: AppTextStyles.heading3),
                    const SizedBox(height: AppPadding.small),
                    Text('Delivery Location:', style: AppTextStyles.bodyMedium.copyWith(fontWeight: FontWeight.bold)),
                    Text('${_demoOrder.deliveryLatitude?.toStringAsFixed(6)}, ${_demoOrder.deliveryLongitude?.toStringAsFixed(6)}'),
                    const SizedBox(height: AppPadding.small),
                    Text('Agent Location:', style: AppTextStyles.bodyMedium.copyWith(fontWeight: FontWeight.bold)),
                    Text('${_demoOrder.currentLatitude?.toStringAsFixed(6)}, ${_demoOrder.currentLongitude?.toStringAsFixed(6)}'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
