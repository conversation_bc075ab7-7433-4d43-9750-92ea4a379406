import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:smart_kirana/models/order_model.dart';
import 'package:smart_kirana/providers/order_provider.dart';
import 'package:smart_kirana/services/location_service.dart';
import 'package:smart_kirana/utils/constants.dart';
import 'package:smart_kirana/widgets/custom_button.dart';
import 'package:smart_kirana/widgets/custom_input_field.dart';

class OrderTrackingAdminScreen extends StatefulWidget {
  final String orderId;

  const OrderTrackingAdminScreen({super.key, required this.orderId});

  @override
  State<OrderTrackingAdminScreen> createState() => _OrderTrackingAdminScreenState();
}

class _OrderTrackingAdminScreenState extends State<OrderTrackingAdminScreen> {
  final _agentNameController = TextEditingController();
  final _agentPhoneController = TextEditingController();
  final LocationService _locationService = LocationService();
  
  bool _isLoading = false;
  OrderModel? _order;

  @override
  void initState() {
    super.initState();
    _loadOrder();
  }

  @override
  void dispose() {
    _agentNameController.dispose();
    _agentPhoneController.dispose();
    super.dispose();
  }

  Future<void> _loadOrder() async {
    final orderProvider = Provider.of<OrderProvider>(context, listen: false);
    await orderProvider.getOrderById(widget.orderId);
    setState(() {
      _order = orderProvider.selectedOrder;
      if (_order != null) {
        _agentNameController.text = _order!.deliveryAgentName ?? '';
        _agentPhoneController.text = _order!.deliveryAgentPhone ?? '';
      }
    });
  }

  Future<void> _updateOrderStatus(OrderStatus status) async {
    setState(() => _isLoading = true);
    
    try {
      final orderProvider = Provider.of<OrderProvider>(context, listen: false);
      final success = await orderProvider.updateOrderStatus(widget.orderId, status);
      
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Order status updated to ${status.name}'),
            backgroundColor: AppColors.success,
          ),
        );
        await _loadOrder();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update status: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _assignDeliveryAgent() async {
    if (_agentNameController.text.trim().isEmpty || 
        _agentPhoneController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter agent name and phone number'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);
    
    try {
      final orderProvider = Provider.of<OrderProvider>(context, listen: false);
      final success = await orderProvider.updateOrderTracking(
        orderId: widget.orderId,
        deliveryAgentName: _agentNameController.text.trim(),
        deliveryAgentPhone: _agentPhoneController.text.trim(),
        estimatedDeliveryTime: DateTime.now().add(const Duration(minutes: 30)),
      );
      
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Delivery agent assigned successfully'),
            backgroundColor: AppColors.success,
          ),
        );
        await _loadOrder();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to assign agent: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _updateCurrentLocation() async {
    setState(() => _isLoading = true);
    
    try {
      // Initialize location service
      final initialized = await _locationService.initialize();
      if (!initialized) {
        throw Exception(_locationService.error ?? 'Failed to initialize location');
      }

      // Get current position
      final position = await _locationService.getCurrentPosition();
      if (position == null) {
        throw Exception('Failed to get current location');
      }

      final orderProvider = Provider.of<OrderProvider>(context, listen: false);
      final success = await orderProvider.updateOrderTracking(
        orderId: widget.orderId,
        currentLatitude: position.latitude,
        currentLongitude: position.longitude,
      );
      
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Location updated successfully'),
            backgroundColor: AppColors.success,
          ),
        );
        await _loadOrder();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update location: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Order Tracking Admin'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: _order == null
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppPadding.medium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Order Info Card
                  _buildOrderInfoCard(),
                  const SizedBox(height: AppPadding.medium),

                  // Status Update Section
                  _buildStatusUpdateSection(),
                  const SizedBox(height: AppPadding.medium),

                  // Delivery Agent Section
                  _buildDeliveryAgentSection(),
                  const SizedBox(height: AppPadding.medium),

                  // Location Update Section
                  _buildLocationUpdateSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildOrderInfoCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppPadding.medium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Order Information', style: AppTextStyles.heading3),
            const SizedBox(height: AppPadding.small),
            Text('Order ID: ${_order!.id.substring(0, 8)}'),
            Text('Customer: ${_order!.userName}'),
            Text('Status: ${_order!.status.name.toUpperCase()}'),
            Text('Total: ₹${_order!.totalAmount.toStringAsFixed(2)}'),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusUpdateSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppPadding.medium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Update Order Status', style: AppTextStyles.heading3),
            const SizedBox(height: AppPadding.medium),
            Wrap(
              spacing: 8,
              children: OrderStatus.values.map((status) {
                final isCurrentStatus = _order!.status == status;
                return ElevatedButton(
                  onPressed: isCurrentStatus || _isLoading 
                      ? null 
                      : () => _updateOrderStatus(status),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: isCurrentStatus 
                        ? AppColors.primary 
                        : AppColors.background,
                    foregroundColor: isCurrentStatus 
                        ? Colors.white 
                        : AppColors.textPrimary,
                  ),
                  child: Text(status.name.toUpperCase()),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeliveryAgentSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppPadding.medium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Delivery Agent', style: AppTextStyles.heading3),
            const SizedBox(height: AppPadding.medium),
            CustomInputField(
              controller: _agentNameController,
              labelText: 'Agent Name',
              prefixIcon: Icons.person,
            ),
            const SizedBox(height: AppPadding.small),
            CustomInputField(
              controller: _agentPhoneController,
              labelText: 'Agent Phone',
              prefixIcon: Icons.phone,
              keyboardType: TextInputType.phone,
            ),
            const SizedBox(height: AppPadding.medium),
            CustomButton(
              text: 'Assign Agent',
              onPressed: _isLoading ? null : _assignDeliveryAgent,
              isLoading: _isLoading,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationUpdateSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppPadding.medium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Location Tracking', style: AppTextStyles.heading3),
            const SizedBox(height: AppPadding.small),
            if (_order!.currentLatitude != null && _order!.currentLongitude != null)
              Text(
                'Current Location: ${_order!.currentLatitude!.toStringAsFixed(6)}, ${_order!.currentLongitude!.toStringAsFixed(6)}',
                style: AppTextStyles.bodySmall,
              ),
            const SizedBox(height: AppPadding.medium),
            CustomButton(
              text: 'Update Current Location',
              onPressed: _isLoading ? null : _updateCurrentLocation,
              isLoading: _isLoading,
              backgroundColor: AppColors.secondary,
            ),
          ],
        ),
      ),
    );
  }
}
