import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:smart_kirana/utils/constants.dart';

class MinimalMapTest extends StatefulWidget {
  const MinimalMapTest({super.key});

  @override
  State<MinimalMapTest> createState() => _MinimalMapTestState();
}

class _MinimalMapTestState extends State<MinimalMapTest> {
  GoogleMapController? _controller;
  
  // Simple test location (Delhi)
  static const CameraPosition _initialPosition = CameraPosition(
    target: LatLng(28.6139, 77.2090),
    zoom: 14.0,
  );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Minimal Map Test'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(AppPadding.medium),
            child: const Text(
              'This is a minimal Google Maps test. If this works, the basic setup is correct.',
              style: TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(AppPadding.medium),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppBorderRadius.medium),
                border: Border.all(color: AppColors.primary),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(AppBorderRadius.medium),
                child: GoogleMap(
                  initialCameraPosition: _initialPosition,
                  onMapCreated: (GoogleMapController controller) {
                    _controller = controller;
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Map loaded successfully!'),
                        backgroundColor: Colors.green,
                        duration: Duration(seconds: 2),
                      ),
                    );
                  },
                  markers: {
                    const Marker(
                      markerId: MarkerId('test_marker'),
                      position: LatLng(28.6139, 77.2090),
                      infoWindow: InfoWindow(
                        title: 'Test Location',
                        snippet: 'Delhi, India',
                      ),
                    ),
                  },
                  myLocationEnabled: false,
                  myLocationButtonEnabled: false,
                  zoomControlsEnabled: true,
                  mapToolbarEnabled: false,
                  compassEnabled: true,
                  trafficEnabled: false,
                  buildingsEnabled: true,
                  indoorViewEnabled: false,
                  mapType: MapType.normal,
                ),
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.all(AppPadding.medium),
            child: Column(
              children: [
                ElevatedButton(
                  onPressed: () {
                    _controller?.animateCamera(
                      CameraUpdate.newCameraPosition(
                        const CameraPosition(
                          target: LatLng(28.6139, 77.2090),
                          zoom: 16.0,
                        ),
                      ),
                    );
                  },
                  child: const Text('Zoom In'),
                ),
                const SizedBox(height: AppPadding.small),
                ElevatedButton(
                  onPressed: () {
                    _controller?.animateCamera(
                      CameraUpdate.newCameraPosition(
                        const CameraPosition(
                          target: LatLng(28.6139, 77.2090),
                          zoom: 12.0,
                        ),
                      ),
                    );
                  },
                  child: const Text('Zoom Out'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
