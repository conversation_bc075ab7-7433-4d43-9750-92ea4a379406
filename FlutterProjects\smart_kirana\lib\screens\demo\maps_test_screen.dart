import 'package:flutter/material.dart';
import 'package:smart_kirana/services/maps_service.dart';
import 'package:smart_kirana/utils/constants.dart';

class MapsTestScreen extends StatefulWidget {
  const MapsTestScreen({super.key});

  @override
  State<MapsTestScreen> createState() => _MapsTestScreenState();
}

class _MapsTestScreenState extends State<MapsTestScreen> {
  final MapsService _mapsService = MapsService();
  bool _isLoading = false;
  String? _error;
  String? _status;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Maps Service Test'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppPadding.medium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppPadding.medium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Maps Service Test', style: AppTextStyles.heading3),
                    const SizedBox(height: AppPadding.medium),
                    
                    if (_isLoading)
                      const Center(child: CircularProgressIndicator())
                    else
                      ElevatedButton(
                        onPressed: _testMapsService,
                        child: const Text('Test Maps Service'),
                      ),
                    
                    const SizedBox(height: AppPadding.medium),
                    
                    if (_status != null) ...[
                      Text('Status:', style: AppTextStyles.bodyMedium.copyWith(fontWeight: FontWeight.bold)),
                      Text(_status!, style: AppTextStyles.bodyMedium),
                      const SizedBox(height: AppPadding.small),
                    ],
                    
                    if (_error != null) ...[
                      Text('Error:', style: AppTextStyles.bodyMedium.copyWith(fontWeight: FontWeight.bold, color: AppColors.error)),
                      Text(_error!, style: AppTextStyles.bodyMedium.copyWith(color: AppColors.error)),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testMapsService() async {
    setState(() {
      _isLoading = true;
      _error = null;
      _status = null;
    });

    try {
      setState(() {
        _status = 'Initializing Maps Service...';
      });

      final initialized = await _mapsService.initialize();
      
      if (initialized) {
        setState(() {
          _status = 'Maps Service initialized successfully!';
        });
      } else {
        setState(() {
          _error = _mapsService.error ?? 'Unknown initialization error';
          _status = 'Maps Service initialization failed';
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Exception during initialization: $e';
        _status = 'Maps Service initialization failed with exception';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
