import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:smart_kirana/models/order_model.dart';
import 'package:smart_kirana/utils/constants.dart';

class SimpleOrderMap extends StatefulWidget {
  final OrderModel order;
  final double height;

  const SimpleOrderMap({
    super.key,
    required this.order,
    this.height = 250,
  });

  @override
  State<SimpleOrderMap> createState() => _SimpleOrderMapState();
}

class _SimpleOrderMapState extends State<SimpleOrderMap> {
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};

  @override
  void initState() {
    super.initState();
    _setupMarkers();
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }

  void _setupMarkers() {
    _markers.clear();

    // Add delivery location marker if available
    if (widget.order.deliveryLatitude != null && 
        widget.order.deliveryLongitude != null) {
      _markers.add(
        Marker(
          markerId: const MarkerId('delivery_location'),
          position: LatLng(
            widget.order.deliveryLatitude!,
            widget.order.deliveryLongitude!,
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
          infoWindow: const InfoWindow(
            title: 'Delivery Location',
            snippet: 'Your order will be delivered here',
          ),
        ),
      );
    }

    // Add current location marker if available
    if (widget.order.currentLatitude != null && 
        widget.order.currentLongitude != null) {
      _markers.add(
        Marker(
          markerId: const MarkerId('current_location'),
          position: LatLng(
            widget.order.currentLatitude!,
            widget.order.currentLongitude!,
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
          infoWindow: InfoWindow(
            title: widget.order.deliveryAgentName ?? 'Delivery Agent',
            snippet: 'Current location',
          ),
        ),
      );
    }
  }

  CameraPosition _getInitialCameraPosition() {
    // Default to delivery location if available
    if (widget.order.deliveryLatitude != null && 
        widget.order.deliveryLongitude != null) {
      return CameraPosition(
        target: LatLng(
          widget.order.deliveryLatitude!,
          widget.order.deliveryLongitude!,
        ),
        zoom: 15.0,
      );
    }

    // Fallback to a default location (Delhi)
    return const CameraPosition(
      target: LatLng(28.6139, 77.2090),
      zoom: 10.0,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.medium),
      ),
      child: Container(
        height: widget.height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppBorderRadius.medium),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(AppBorderRadius.medium),
          child: _buildMapContent(),
        ),
      ),
    );
  }

  Widget _buildMapContent() {
    // Check if we have location data
    if (widget.order.deliveryLatitude == null || 
        widget.order.deliveryLongitude == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.location_off,
              size: 48,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: AppPadding.small),
            Text(
              'Location Not Available',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppPadding.small / 2),
            Text(
              'Delivery location will be updated soon',
              style: AppTextStyles.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    try {
      return GoogleMap(
        initialCameraPosition: _getInitialCameraPosition(),
        markers: _markers,
        onMapCreated: (GoogleMapController controller) {
          _mapController = controller;
        },
        myLocationEnabled: false,
        myLocationButtonEnabled: false,
        zoomControlsEnabled: true,
        mapToolbarEnabled: false,
        compassEnabled: true,
        trafficEnabled: false,
        buildingsEnabled: true,
        indoorViewEnabled: false,
        mapType: MapType.normal,
      );
    } catch (e) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 48,
              color: AppColors.error,
            ),
            const SizedBox(height: AppPadding.small),
            Text(
              'Map Error',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppPadding.small / 2),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppPadding.medium),
              child: Text(
                'Failed to load map: $e',
                style: AppTextStyles.bodySmall,
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      );
    }
  }
}
